<?php

use Livewire\Volt\Component;
use Illuminate\Support\Collection;
use App\Models\Product;

new class extends Component {
    /**
    * @param Collection<Product> $products
    */
    public Collection $products;

    public function mount(): void
    {
        $this->products = Product::all();
    }
}; ?>

<section class="grid mx-auto md:mx-0 md:grid-cols-[60%_40%]">
    <div class="container flex flex-col overflow-hidden">
            @foreach ($products as $product)
            <div class="hover:shadow-2xl shadow-xl my-2 rounded-t-2xl rounded-b-xl cursor-pointer">
                <div class="relative h-16 w-full flex flex-row justify-between items-center bg-pink-500/25 border-2 border-pink-600 rounded-t-2xl">
                    <button class="cursor-pointer flex mx-5 items-center justify-center size-12 md:bg-green-300 md:shadow md:hover:shadow-lg rounded-full"
                        onmousedown="document.getElementById('product-content-{{ $product->id }}').classList.replace('md:grid-rows-[0fr]', 'md:grid-rows-[1fr]');
                            document.getElementById('product-content-{{ $product->id }}').classList.replace('md:grid-rows-[0fr]', 'md:grid-rows-[1fr]');
                            document.getElementById('product-tab-{{ $product->id }}').classList.replace('opacity-100', 'opacity-0');
                            ">
                        <div class="size-8 hidden md:block cursor-pointer">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><rect width="16" height="16" x="4" y="4" stroke="oklch(62.7% 0.194 149.214)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" rx="2"/></svg>
                        </div>
                    </button>
                    <div id="product-tab-{{ $product->id }}" class="absolute opacity-0 -bottom-4 left-20 w-fit text-center px-3 pt-2 h-3/4 border-t-2 border-x-2 border-t-pink-600 border-x-pink-600 self-end overflow-hidden text-ellipsis whitespace-nowrap rounded-t-lg text-sm font-medium bg-white text-zinc-700 dark:text-zinc-300 transition-all ease-in duration-200">
                        {{ $product->name }}
                    </div>
                    <button class="cursor-pointer flex mx-5 items-center justify-center size-12 md:bg-gray-200 md:shadow md:hover:shadow-lg rounded-full"
                        onmousedown="document.getElementById('product-content-{{ $product->id }}').classList.toggle('md:grid-rows-[0fr]');
                            document.getElementById('product-content-{{ $product->id }}').classList.toggle('md:grid-rows-[1fr]');
                            document.getElementById('product-tab-{{ $product->id }}').classList.toggle('opacity-100');
                            document.getElementById('product-tab-{{ $product->id }}').classList.toggle('-bottom-[3px]');
                            documebt.getElementById('product-body-{{ $product->id }}').classList.add('hidden');
                            ">
                        <div class="size-8 hidden md:block">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path fill="#99a1af" d="M20.746 3.329a1 1 0 0 0-1.415 0l-7.294 7.294-7.294-7.294a1 1 0 1 0-1.414 1.414l7.294 7.294-7.294 7.294a1 1 0 0 0 1.414 1.415l7.294-7.295 7.294 7.295a1 1 0 0 0 1.415-1.415l-7.295-7.294 7.295-7.294a1 1 0 0 0 0-1.414Z"/></svg>
                        </div>
                    </button>
                </div>
                <div id="product-content-{{ $product->id }}" class="grid md:grid-rows-[1fr] p-2 duration-500 border border-pink-600/50 rounded-b-xl">
                    <div class="flex flex-col overflow-hidden md:flex-row w-full" width="300px" height="150px">
                        <img class="block m-4 h-56 w-96" src="{{ asset("storage/".$product->images->first()->image) }}" alt="{{ $product->name }}"></img>
                        <p class="block border-t pt-4 mt-4 md:border-l md:pl-6 md:pt-0 md:m-4 md:mt-0 md:border-t-0">
                            <span class="block text-2xl">{{ $product->name }}</span>
                            <span class="block">{{ $product->description }}</p>
                        </p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
